#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
test_semantic_chunker_optimization.py

测试语义分块器优化效果的脚本
"""

import logging
import time
import psutil
import os
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_memory_usage():
    """获取当前内存使用量（MB）"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_chunker_performance(chunker, content, project_name, test_name):
    """测试分块器性能"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"{'='*60}")
    
    # 记录初始内存
    initial_memory = get_memory_usage()
    print(f"初始内存使用: {initial_memory:.2f} MB")
    
    # 执行分块
    start_time = time.time()
    result = chunker.preprocess_document(content, project_name)
    end_time = time.time()
    
    # 记录结束内存
    final_memory = get_memory_usage()
    memory_increase = final_memory - initial_memory
    
    # 输出结果
    print(f"处理结果: {'成功' if result.success else '失败'}")
    if not result.success:
        print(f"错误信息: {result.error_message}")
    else:
        print(f"原始长度: {result.original_length:,} 字符")
        print(f"处理后长度: {result.preprocessed_length:,} 字符")
        print(f"分块数量: {result.chunk_count}")
        print(f"优化方式: {result.optimization_used}")
    
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"内存增长: {memory_increase:.2f} MB")
    print(f"最终内存使用: {final_memory:.2f} MB")
    
    return {
        'success': result.success,
        'processing_time': end_time - start_time,
        'memory_increase': memory_increase,
        'chunk_count': result.chunk_count if result.success else 0,
        'optimization_used': result.optimization_used if result.success else 'failed'
    }

def create_test_content(size_multiplier=1):
    """创建测试内容"""
    base_content = """<img alt="undefined" src="images_test_page_1_img_1.png">
四种菜品中，价格涨幅最大的是(   )。
菜品A
菜品B
菜品C
<span style="color:rgb(255, 0, 0)"><strong>菜品D</strong></span>

根据2015-2019年全国生猪出栏量情况和2015-2019年国内猪肉产量占肉类总产量的比例情况图回答下列问题。
<img alt="undefined" src="images_test_page_2_img_1.png">
由于"非洲猪瘟"影响，2019年全国生猪出栏量明显下降，国家为了稳定猪肉价，大力推进生猪养殖，从2020年起全国生猪出栏量每年按照10%的速度增长，问到()年全国生猪出栏量超过2015年水准(   )
2025
2024
2023
<span style="color:rgb(255, 0, 0)"><strong>2022</strong></span>

下图反映了某种产品的成本与出厂价情况(单位:元)。根据图表回答下列问题:
<img alt="undefined" src="images_test_page_3_img_1.png">
每生产一件该产品获得的利润率最高的是第(   )年
二
三
<span style="color:rgb(255, 0, 0)"><strong>四</strong></span>
五

下图是过去某两年，手机用户和PC/平板用户，自主安装浏览器的情况。请根据图表信息，回答问题。
<img alt="undefined" src="images_test_page_4_img_1.png">
第一年，用户自主安装的PC/平板浏览器中，前两名的市场份额之和为(   )。
42.5%
<span style="color:rgb(255, 0, 0)"><strong>43%</strong></span>
48%
66%

图表是我国2月份全社会客货运输量的信息。请根据图表相关信息，回答问题。
<img alt="undefined" src="images_test_page_5_img_1.png">
如果明年2月份，货运总量的增长速度是水运货运量增长速度的2.5倍，那么明年2月份的货运总量约为(   )。
42亿吨
<span style="color:rgb(255, 0, 0)"><strong>43亿吨</strong></span>
99亿吨
98亿吨

10月份，几个不同移动教育应用呈现出了不同的覆盖率和活跃率，具体如下。请根据图表信息，回答问题。
<img alt="undefined" src="images_test_page_6_img_1.png">
10月份不同移动教育应用中，活跃率与覆盖率之比最大的是(   )。
作业帮
<span style="color:rgb(255, 0, 0)"><strong>阿凡题</strong></span>
纳米盒
我要当学霸
"""
    
    # 根据倍数重复内容
    return base_content * size_multiplier

def main():
    """主测试函数"""
    print("语义分块器优化效果测试")
    print("=" * 60)
    
    # 导入必要的模块
    from multi_channel_config import MultiChannelConfigParser
    from ai_service import AIService
    from semantic_chunker import SemanticChunker
    
    # 加载配置
    config_parser = MultiChannelConfigParser()
    config = config_parser.get_config()
    
    # 创建AI服务
    ai_service = AIService(config)
    
    # 创建测试内容（不同大小）
    test_cases = [
        ("小文档", create_test_content(1)),
        ("中等文档", create_test_content(3)),
        ("大文档", create_test_content(6))
    ]
    
    results = {}
    
    for test_name, content in test_cases:
        print(f"\n{'#'*80}")
        print(f"测试用例: {test_name} (长度: {len(content):,} 字符)")
        print(f"{'#'*80}")
        
        # 测试1: 原始实现（禁用AI分块）
        config_original = config.copy()
        config_original['enable_ai_chunking'] = False
        chunker_original = SemanticChunker(ai_service, config=config_original)
        result_original = test_chunker_performance(
            chunker_original, content, f"test_original_{test_name.lower()}",
            f"{test_name} - 规则驱动分块"
        )

        # 测试2: 统一优化实现（AI分块 + 字符串优化 + 并行处理）
        config_optimized = config.copy()
        config_optimized['enable_ai_chunking'] = True
        chunker_optimized = SemanticChunker(ai_service, config=config_optimized)
        result_optimized = test_chunker_performance(
            chunker_optimized, content, f"test_optimized_{test_name.lower()}",
            f"{test_name} - 统一优化AI分块"
        )

        # 保存结果
        results[test_name] = {
            'original': result_original,
            'optimized': result_optimized
        }
    
    # 输出性能对比总结
    print(f"\n{'='*80}")
    print("性能对比总结")
    print(f"{'='*80}")
    
    for test_name, test_results in results.items():
        print(f"\n{test_name}:")
        print("-" * 40)

        original = test_results['original']
        optimized = test_results['optimized']

        if original['success'] and optimized['success']:
            time_improvement = ((original['processing_time'] - optimized['processing_time']) / original['processing_time']) * 100
            memory_improvement = ((original['memory_increase'] - optimized['memory_increase']) / original['memory_increase']) * 100 if original['memory_increase'] > 0 else 0

            print(f"统一优化效果 (AI分块 + 字符串优化 + 并行处理):")
            print(f"  时间改善: {time_improvement:+.1f}%")
            print(f"  内存改善: {memory_improvement:+.1f}%")
            print(f"  优化方式: {optimized['optimization_used']}")
            print(f"  分块数量: {original['chunk_count']} → {optimized['chunk_count']}")
        elif not original['success']:
            print(f"规则驱动分块失败: {original.get('error_message', '未知错误')}")
        elif not optimized['success']:
            print(f"优化AI分块失败: {optimized.get('error_message', '未知错误')}")
        else:
            print("无法比较结果")

if __name__ == "__main__":
    main()
