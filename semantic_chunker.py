#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
semantic_chunker.py

语义分块器 - 实现AI驱动的智能内容分块
Stage 1: AI预处理，在源Markdown内容中插入语义分块标记
"""

import logging
import time
import io
import re
import gc
from pathlib import Path
from typing import Dict, Any, Optional, List, NamedTuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from difflib import SequenceMatcher


class BoundaryWindow(NamedTuple):
    """边界窗口数据结构"""
    id: int
    window_content: str
    window_start: int
    window_end: int
    ideal_pos: int


class ProcessedWindow(NamedTuple):
    """处理后的窗口数据结构"""
    window_id: int
    processed_content: Optional[str]
    success: bool
    error_message: str = ""


@dataclass
class ChunkingResult:
    """分块结果数据类"""
    success: bool
    preprocessed_content: str
    chunk_count: int
    processing_time: float
    error_message: str = ""
    original_length: int = 0
    preprocessed_length: int = 0
    optimization_used: str = "none"  # 记录使用的优化方式


class SemanticChunker:
    """
    语义分块器
    
    使用AI分析文档内容，在语义边界处插入分块标记
    """
    
    def __init__(self, ai_service, chunk_marker: str = "<!-- CHUNK_BREAK -->", config: dict = None):
        """
        初始化语义分块器

        Args:
            ai_service: AI服务实例
            chunk_marker: 分块标记字符串
            config: 配置字典
        """
        self.ai_service = ai_service
        self.chunk_marker = chunk_marker
        self.config = config or {}

        # AI分块配置参数 - 支持新的动态配置系统（小写键名）和旧的配置系统（大写键名）
        self.enable_ai_chunking = self._get_config_bool('enable_ai_chunking', 'ENABLE_AI_CHUNKING', True)

        # 目标块大小，我们期望每个分块大约这么大
        self.ai_chunking_target_size = self._get_config_int('ai_chunking_target_size', 'AI_CHUNKING_TARGET_SIZE', 800)

        # 发送给AI进行"智能剪刀"操作的上下文窗口大小
        # 如果配置中没有这个参数，使用目标大小的1/4作为默认值
        context_window_default = self.ai_chunking_target_size // 4
        self.ai_chunking_context_window_size = self._get_config_int('ai_chunking_context_window_size', 'AI_CHUNKING_CONTEXT_WINDOW_SIZE', context_window_default)

        self.ai_chunking_timeout = self._get_config_int('ai_chunking_timeout', 'AI_CHUNKING_TIMEOUT', 180)
        self.ai_chunking_fallback_to_rules = self._get_config_bool('ai_chunking_fallback_to_rules', 'AI_CHUNKING_FALLBACK_TO_RULES', True)

        # 优化配置参数（统一优化，只保留必要的配置）
        self.max_parallel_workers = self._get_config_int('max_parallel_workers', 'MAX_PARALLEL_WORKERS', 3)
        self.parallel_processing_min_content_size = self._get_config_int('parallel_processing_min_content_size', 'PARALLEL_PROCESSING_MIN_CONTENT_SIZE', 5000)
        self.parallel_assembly_timeout = self._get_config_int('parallel_assembly_timeout', 'PARALLEL_ASSEMBLY_TIMEOUT', 30)
        
        # <<< 新增：智能剪刀提示词 >>>
        self.smart_clipping_system_prompt = """# 角色
你是一个文本智能分块顾问。你的任务是检查一小段文本片段，并在其中找到**唯一一个最合适**的分割点。

# 我的工作流程 (为你提供上下文)
1.  我正在处理一个非常巨大的文本文件，里面包含许多"问答块"。
2.  我无法一次性处理整个文件。我会用一个固定的长度来预设一个分割点。
3.  为了避免在问题或答案中间切断，我会从预设分割点前后截取一段文本（即你现在收到的内容）发送给你。
4.  你的任务就是在这段文本中，找到那个最符合逻辑、最"干净"的边界，并用 `<!-- CHUNK_BREAK -->` 标记出来。

# 你的核心任务
分析我提供的文本片段，并在其中插入**一个且仅一个** `<!-- CHUNK_BREAK -->` 标记。

# 如何选择最佳分割点
1.  **识别边界信号**：一个"问答块"的结束标志是其被高亮标记的正确答案（例如：`<span style="color:rgb(255, 0, 0)"><strong>...</strong></span>`）。下一个"问答块"的开始标志是题号（如 `3.`）、引导性文字或图片。最佳分割点就在这两者之间。
2.  **选择唯一位置**：
    *   在给定的文本片段中，可能会有多个潜在的分割点。
    *   你应该选择**最接近文本片段中心位置**的那个分割点。这能最好地满足我初步设定的分块大小。
    *   如果整个片段只有一个清晰的分割点，那就选择那一个。
3.  **最终输出**：返回给我带有**一个** `<!-- CHUNK_BREAK -->` 标记的文本。
4.  **无合适分割点的情况**：如果在提供的文本片段中找不到任何一个完整的"问答块"边界（例如，片段的开头和结尾都在一个问题的内部），请直接返回原始文本，不要添加任何标记。
"""
        self.smart_clipping_task_prompt = """# 执行任务
请根据以上所有规则，处理我接下来提供的文本片段，找到唯一最佳的分割点并插入 `<!-- CHUNK_BREAK -->`。

---
{content}
---
"""

        chunking_mode = "AI驱动" if self.enable_ai_chunking else "规则驱动"
        logging.info(f"语义分块器初始化完成，模式: {chunking_mode}, 分块标记: {self.chunk_marker}")
        logging.info(f"AI分块配置: 目标大小={self.ai_chunking_target_size}, 上下文窗口={self.ai_chunking_context_window_size}, 超时={self.ai_chunking_timeout}s")

    def _get_config_bool(self, new_key: str, old_key: str, default: bool) -> bool:
        """
        获取布尔配置值，支持新旧配置键名

        Args:
            new_key: 新的配置键名（小写下划线格式）
            old_key: 旧的配置键名（大写格式）
            default: 默认值
        """
        # 优先使用新的键名
        if new_key in self.config:
            value = self.config[new_key]
            if isinstance(value, bool):
                return value
            elif isinstance(value, str):
                return value.lower() in ['true', '1', 'yes', 'on']

        # 回退到旧的键名
        if old_key in self.config:
            value = self.config[old_key]
            if isinstance(value, bool):
                return value
            elif isinstance(value, str):
                return value.lower() in ['true', '1', 'yes', 'on']

        return default

    def _get_config_int(self, new_key: str, old_key: str, default: int) -> int:
        """
        获取整数配置值，支持新旧配置键名

        Args:
            new_key: 新的配置键名（小写下划线格式）
            old_key: 旧的配置键名（大写格式）
            default: 默认值
        """
        # 优先使用新的键名
        if new_key in self.config:
            value = self.config[new_key]
            if isinstance(value, int):
                return value
            elif isinstance(value, str):
                try:
                    return int(value)
                except ValueError:
                    pass

        # 回退到旧的键名
        if old_key in self.config:
            value = self.config[old_key]
            if isinstance(value, int):
                return value
            elif isinstance(value, str):
                try:
                    return int(value)
                except ValueError:
                    pass

        return default

    def _get_config_float(self, new_key: str, old_key: str, default: float) -> float:
        """
        获取浮点数配置值，支持新旧配置键名

        Args:
            new_key: 新的配置键名（小写下划线格式）
            old_key: 旧的配置键名（大写格式）
            default: 默认值
        """
        # 优先使用新的键名
        if new_key in self.config:
            value = self.config[new_key]
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                try:
                    return float(value)
                except ValueError:
                    pass

        # 回退到旧的键名
        if old_key in self.config:
            value = self.config[old_key]
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                try:
                    return float(value)
                except ValueError:
                    pass

        return default

    def _get_config_float(self, new_key: str, old_key: str, default: float) -> float:
        """获取浮点数配置值，支持新旧键名"""
        # 优先使用新的键名
        if new_key in self.config:
            value = self.config[new_key]
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                try:
                    return float(value)
                except ValueError:
                    pass

        # 回退到旧的键名
        if old_key in self.config:
            value = self.config[old_key]
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                try:
                    return float(value)
                except ValueError:
                    pass

        return default

    def preprocess_document(self, content: str, project_name: str) -> ChunkingResult:
        """
        预处理文档，插入语义分块标记
        """
        start_time = time.time()
        original_length = len(content)

        logging.info(f"开始语义分块预处理: 项目={project_name}, 原始长度={original_length}字符")

        if original_length == 0:
            return ChunkingResult(success=False, preprocessed_content="", chunk_count=0, processing_time=0, error_message="输入内容为空")

        # 如果内容本身就小于目标块大小，不需要分块
        if original_length < self.ai_chunking_target_size * 1.2: # 增加一个buffer
            logging.info(f"内容较短({original_length}字符)，跳过语义分块")
            return ChunkingResult(
                success=True, preprocessed_content=content, chunk_count=1, processing_time=time.time() - start_time,
                original_length=original_length, preprocessed_length=original_length)

        try:
            optimization_used = "none"
            if self.enable_ai_chunking:
                # 统一使用优化的AI分块策略（字符串优化+并行处理）
                if original_length >= self.parallel_processing_min_content_size:
                    logging.info("使用AI驱动的优化并行语义分块 (字符串优化+并行处理)")
                    preprocessed_content = self._optimized_parallel_ai_chunking(content, project_name)
                    optimization_used = "optimized_parallel"
                else:
                    logging.info("使用AI驱动的优化语义分块 (字符串优化)")
                    preprocessed_content = self._optimized_ai_driven_chunking(content, project_name)
                    optimization_used = "optimized_sequential"
            else:
                logging.info("使用规则驱动的语义分块")
                preprocessed_content = self._rule_based_semantic_chunking(content, project_name)
                optimization_used = "rule_based"

            chunk_count = self._count_chunks(preprocessed_content)
            processing_time = time.time() - start_time
            preprocessed_length = len(preprocessed_content)

            logging.info(f"语义分块完成: 生成{chunk_count}个块, 处理时间{processing_time:.2f}s, 优化方式={optimization_used}")
            return ChunkingResult(
                success=True, preprocessed_content=preprocessed_content, chunk_count=chunk_count,
                processing_time=processing_time, original_length=original_length,
                preprocessed_length=preprocessed_length, optimization_used=optimization_used)

        except Exception as e:
            error_msg = f"语义分块处理异常: {str(e)}"
            logging.error(error_msg, exc_info=True)
            return ChunkingResult(
                success=False, preprocessed_content=content, chunk_count=1, processing_time=time.time() - start_time,
                error_message=error_msg, original_length=original_length, preprocessed_length=original_length)

    def _ai_driven_semantic_chunking(self, content: str, project_name: str) -> str:
        """
        AI驱动的智能语义分块（滑动窗口策略）
        todo：优化这里的算法，并不需要那么多的字符串copy
        """
        final_content = []
        remaining_content = content

        while len(remaining_content) > self.ai_chunking_target_size * 1.2: # 1.2倍buffer避免产生过小尾部
            try:
                # 1. 确定理想的分割点位置
                ideal_split_pos = self.ai_chunking_target_size

                # 2. 围绕理想分割点，构建发送给AI的上下文窗口
                half_window = self.ai_chunking_context_window_size // 2
                start = max(0, ideal_split_pos - half_window)
                end = min(len(remaining_content), ideal_split_pos + half_window)
                context_window = remaining_content[start:end]

                logging.info(f"创建上下文窗口进行AI分析: 范围 [{start}:{end}], 窗口大小 {len(context_window)}")

                # 3. 调用AI在窗口内找到最佳分割点
                processed_window = self._call_ai_for_chunking(
                    content=context_window,
                    system_prompt=self.smart_clipping_system_prompt,
                    task_prompt_template=self.smart_clipping_task_prompt
                )

                # 4. 在处理后的窗口中找到AI插入的标记
                marker_pos_in_window = processed_window.find(self.chunk_marker)

                split_pos_in_original = -1
                if marker_pos_in_window != -1:
                    # 如果AI成功插入标记，计算该标记在`remaining_content`中的绝对位置
                    # 我们在标记之前进行分割，所以分割位置就是标记在原始内容中的位置
                    split_pos_in_original = start + marker_pos_in_window
                    logging.info(f"AI找到分割点，位于窗口内 {marker_pos_in_window}，原始文本中 {split_pos_in_original}")

                    # 5a. AI成功插入标记的情况：直接使用AI处理后的窗口内容
                    # 提取标记前的内容作为当前块
                    chunk_content = remaining_content[:split_pos_in_original]
                    final_content.append(chunk_content)

                    # 跳过标记，继续处理剩余内容
                    # 注意：这里需要从AI处理后的窗口中提取标记后的内容，而不是简单跳过标记长度
                    marker_end_in_window = marker_pos_in_window + len(self.chunk_marker)
                    remaining_content_from_window = processed_window[marker_end_in_window:]
                    # 将窗口后的内容与原始内容中窗口后的部分合并
                    remaining_content = remaining_content_from_window + remaining_content[end:]

                    # 添加标记到最终内容中
                    final_content.append(self.chunk_marker)

                else:
                    # AI没有找到合适的分割点，采用硬分割作为后备
                    split_pos_in_original = ideal_split_pos
                    logging.warning(f"AI未在窗口内找到分割点，回退到目标位置 {ideal_split_pos} 进行硬分割")

                    # 5b. 硬分割的情况
                    chunk = remaining_content[:split_pos_in_original]
                    final_content.append(chunk)
                    remaining_content = remaining_content[split_pos_in_original:]
                    # 手动插入标记
                    final_content.append(self.chunk_marker)

            except Exception as e:
                logging.error(f"AI滑动窗口分块失败: {e}", exc_info=True)
                if self.ai_chunking_fallback_to_rules:
                    logging.warning("AI分块失败，回退到规则驱动分块处理剩余内容")
                    # 对于剩余部分，使用规则分块
                    remaining_processed = self._rule_based_semantic_chunking(remaining_content, project_name)
                    final_content.append(remaining_processed)
                    remaining_content = "" # 终止循环
                    break
                else:
                    # 如果不回退，则将剩余内容直接附加并停止
                    final_content.append(remaining_content)
                    remaining_content = ""
                    logging.error("AI分块失败且未配置回退，已停止分块。")
                    break

        # 将最后剩余的部分附加到结果中
        if remaining_content:
            final_content.append(remaining_content)

        return "".join(final_content)

    def _optimized_ai_driven_chunking(self, content: str, project_name: str) -> str:
        """
        字符串优化版本的AI驱动语义分块
        使用索引操作和io.StringIO减少字符串复制
        """
        content_length = len(content)
        current_pos = 0
        result_buffer = io.StringIO()

        logging.info(f"开始字符串优化AI分块: 内容长度={content_length}, 目标块大小={self.ai_chunking_target_size}")

        while current_pos < content_length - self.ai_chunking_target_size * 1.2:
            try:
                # 计算理想分割位置和窗口边界
                ideal_split_pos = current_pos + self.ai_chunking_target_size
                half_window = self.ai_chunking_context_window_size // 2
                window_start = max(current_pos, ideal_split_pos - half_window)
                window_end = min(content_length, ideal_split_pos + half_window)

                # 只为AI处理创建必要的子字符串
                context_window = content[window_start:window_end]

                logging.debug(f"处理窗口: [{window_start}:{window_end}], 窗口大小={len(context_window)}")

                # AI处理窗口
                processed_window = self._call_ai_for_chunking(
                    content=context_window,
                    system_prompt=self.smart_clipping_system_prompt,
                    task_prompt_template=self.smart_clipping_task_prompt
                )

                # 查找标记位置
                marker_pos_in_window = processed_window.find(self.chunk_marker)

                if marker_pos_in_window != -1:
                    # AI找到分割点
                    split_pos_in_original = window_start + marker_pos_in_window
                    logging.debug(f"AI找到分割点: 窗口内位置={marker_pos_in_window}, 原文位置={split_pos_in_original}")

                    # 直接写入块内容到缓冲区
                    result_buffer.write(content[current_pos:split_pos_in_original])
                    result_buffer.write(self.chunk_marker)

                    # 更新当前位置
                    current_pos = split_pos_in_original
                else:
                    # 回退到理想位置进行硬分割
                    logging.debug(f"AI未找到分割点，使用理想位置={ideal_split_pos}")
                    result_buffer.write(content[current_pos:ideal_split_pos])
                    result_buffer.write(self.chunk_marker)
                    current_pos = ideal_split_pos

            except Exception as e:
                logging.error(f"优化AI分块失败: {e}", exc_info=True)
                if self.ai_chunking_fallback_to_rules:
                    logging.warning("回退到规则驱动分块处理剩余内容")
                    remaining_content = content[current_pos:]
                    remaining_processed = self._rule_based_semantic_chunking(remaining_content, project_name)
                    result_buffer.write(remaining_processed)
                    break
                else:
                    # 直接写入剩余内容
                    result_buffer.write(content[current_pos:])
                    break

        # 写入最后剩余的内容
        if current_pos < content_length:
            result_buffer.write(content[current_pos:])

        return result_buffer.getvalue()

    def _optimized_parallel_ai_chunking(self, content: str, project_name: str) -> str:
        """
        统一优化版本：字符串优化 + 并行处理
        同时处理多个边界窗口，使用io.StringIO进行高效字符串构建
        """
        content_length = len(content)
        logging.info(f"开始优化并行AI分块: 内容长度={content_length}, 目标块大小={self.ai_chunking_target_size}")

        # 1. 预计算所有边界窗口
        boundary_windows = self._create_boundary_windows(content)
        if not boundary_windows:
            logging.warning("未创建任何边界窗口，回退到优化顺序分块")
            return self._optimized_ai_driven_chunking(content, project_name)

        logging.info(f"创建了 {len(boundary_windows)} 个边界窗口进行并行处理")

        # 2. 分批并行处理窗口
        all_positions = []
        batch_size = min(self.max_parallel_workers * 2, len(boundary_windows))

        for batch_start in range(0, len(boundary_windows), batch_size):
            batch_end = min(batch_start + batch_size, len(boundary_windows))
            batch_windows = boundary_windows[batch_start:batch_end]

            logging.debug(f"处理批次 {batch_start//batch_size + 1}: 窗口 {batch_start}-{batch_end-1}")
            batch_positions = self._process_window_batch_optimized(content, batch_windows)
            all_positions.extend(batch_positions)

            # 内存清理
            gc.collect()

        # 3. 解决位置冲突并使用字符串优化组装结果
        resolved_positions = self._resolve_position_conflicts(all_positions)
        logging.info(f"解决冲突后得到 {len(resolved_positions)} 个有效分块位置")

        return self._insert_markers_at_positions_optimized(content, resolved_positions)

    def _process_window_batch_optimized(self, content: str, windows: List[BoundaryWindow]) -> List[int]:
        """优化版本的批量窗口处理"""
        max_workers = min(len(windows), self.max_parallel_workers)
        positions = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有窗口处理任务
            future_to_window = {
                executor.submit(self._process_single_window, window): window
                for window in windows
            }

            # 收集结果
            for future in as_completed(future_to_window, timeout=self.parallel_assembly_timeout):
                window = future_to_window[future]
                try:
                    processed_result = future.result()
                    if processed_result.success:
                        position = self._find_insertion_position_optimized(content, processed_result, window)
                        if position is not None:
                            positions.append(position)
                            logging.debug(f"窗口 {window.id} 找到插入位置: {position}")
                        else:
                            # 使用理想位置作为回退
                            positions.append(window.ideal_pos)
                            logging.debug(f"窗口 {window.id} 使用理想位置: {window.ideal_pos}")
                    else:
                        # 处理失败，使用理想位置
                        positions.append(window.ideal_pos)
                        logging.warning(f"窗口 {window.id} 处理失败，使用理想位置: {processed_result.error_message}")

                except Exception as e:
                    logging.error(f"窗口 {window.id} 结果处理异常: {e}")
                    positions.append(window.ideal_pos)

        return positions

    def _find_insertion_position_optimized(self, original_content: str, processed_result: ProcessedWindow, window: BoundaryWindow) -> Optional[int]:
        """优化版本的插入位置查找"""
        if not processed_result.processed_content or self.chunk_marker not in processed_result.processed_content:
            return None

        marker_pos = processed_result.processed_content.find(self.chunk_marker)
        text_before_marker = processed_result.processed_content[:marker_pos]

        # 策略1: 精确匹配（最高效）
        exact_match = original_content.find(text_before_marker)
        if exact_match != -1:
            return exact_match + len(text_before_marker)

        # 策略2: 在窗口范围内精确匹配（提高效率）
        search_start = max(0, window.window_start - 100)
        search_end = min(len(original_content), window.window_end + 100)
        search_content = original_content[search_start:search_end]
        local_match = search_content.find(text_before_marker)
        if local_match != -1:
            return search_start + local_match + len(text_before_marker)

        # 策略3: 模糊匹配（仅在必要时使用）
        return self._fuzzy_match_position_optimized(original_content, text_before_marker, window)

    def _fuzzy_match_position_optimized(self, original_content: str, text_before_marker: str, window: BoundaryWindow) -> Optional[int]:
        """优化版本的模糊匹配"""
        if len(text_before_marker) < 20:  # 文本太短，不适合模糊匹配
            return None

        # 缩小搜索范围以提高效率
        search_start = max(0, window.window_start - 50)
        search_end = min(len(original_content), window.window_end + 50)

        best_ratio = 0.8  # 提高阈值
        best_pos = None

        # 使用更大的步长以提高效率
        step_size = max(20, len(text_before_marker) // 10)

        for i in range(search_start, search_end - len(text_before_marker), step_size):
            candidate = original_content[i:i + len(text_before_marker)]
            ratio = SequenceMatcher(None, text_before_marker, candidate).ratio()
            if ratio > best_ratio:
                best_ratio = ratio
                best_pos = i + len(text_before_marker)

        return best_pos

    def _insert_markers_at_positions_optimized(self, content: str, positions: List[int]) -> str:
        """优化版本的标记插入（使用io.StringIO）"""
        if not positions:
            return content

        result_buffer = io.StringIO()
        last_pos = 0

        for pos in positions:
            result_buffer.write(content[last_pos:pos])
            result_buffer.write(self.chunk_marker)
            last_pos = pos

        # 添加最后一部分
        result_buffer.write(content[last_pos:])

        return result_buffer.getvalue()



    def _create_boundary_windows(self, content: str) -> List[BoundaryWindow]:
        """创建所有边界窗口"""
        content_length = len(content)
        windows = []
        current_pos = 0
        window_id = 0

        while current_pos < content_length - self.ai_chunking_target_size * 1.2:
            ideal_split_pos = current_pos + self.ai_chunking_target_size
            half_window = self.ai_chunking_context_window_size // 2
            window_start = max(0, ideal_split_pos - half_window)
            window_end = min(content_length, ideal_split_pos + half_window)

            window = BoundaryWindow(
                id=window_id,
                window_content=content[window_start:window_end],
                window_start=window_start,
                window_end=window_end,
                ideal_pos=ideal_split_pos
            )
            windows.append(window)

            current_pos += self.ai_chunking_target_size
            window_id += 1

        return windows



    def _process_single_window(self, window: BoundaryWindow) -> ProcessedWindow:
        """处理单个窗口（线程安全）"""
        try:
            processed_content = self._call_ai_for_chunking(
                content=window.window_content,
                system_prompt=self.smart_clipping_system_prompt,
                task_prompt_template=self.smart_clipping_task_prompt
            )
            return ProcessedWindow(
                window_id=window.id,
                processed_content=processed_content,
                success=True
            )
        except Exception as e:
            return ProcessedWindow(
                window_id=window.id,
                processed_content=None,
                success=False,
                error_message=str(e)
            )



    def _call_ai_for_chunking(self, content: str, system_prompt: str, task_prompt_template: str) -> str:
        """
        调用AI服务执行语义分块任务
        """
        try:
            result = self.ai_service.call_raw_api(
                content=content,
                system_prompt=system_prompt,
                task_prompt=task_prompt_template,
                timeout=self.ai_chunking_timeout
            )
            logging.debug(f"AI语义分块调用完成: 输出长度={len(result)}")
            return result
        except Exception as e:
            logging.error(f"AI语义分块调用失败: {e}")
            raise e

    def _rule_based_semantic_chunking(self, content: str, project_name: str) -> str:
        """
        规则驱动的语义分块（作为备用方案）
        在固定长度附近寻找换行符进行分割。
        """
        logging.info(f"开始规则驱动语义分块: 项目={project_name}, 目标块大小={self.ai_chunking_target_size}")

        result_parts = []
        remaining_content = content

        while len(remaining_content) > self.ai_chunking_target_size:
            # 找到目标位置附近最接近的换行符
            split_pos = remaining_content.rfind('\n', 0, self.ai_chunking_target_size)
            if split_pos == -1: # 如果找不到，就在目标位置硬切
                split_pos = self.ai_chunking_target_size

            result_parts.append(remaining_content[:split_pos])
            result_parts.append(f"\n{self.chunk_marker}\n")
            remaining_content = remaining_content[split_pos:].lstrip('\n')

        result_parts.append(remaining_content)

        return "".join(result_parts)

    def _count_chunks(self, content: str) -> int:
        """计算分块数量"""
        if not content:
            return 0
        return content.count(self.chunk_marker) + 1

    # 以下方法保持不变...
    def get_preprocessed_file_path(self, project_name: str, base_dir: Path) -> Path:
        """获取预处理文件路径"""
        project_dir = base_dir / "extracted" / project_name
        md_files = list(project_dir.glob("*.md"))
        if not md_files:
            raise FileNotFoundError(f"项目 {project_name} 中没有找到markdown文件")
        original_md = md_files[0]
        preprocessed_name = original_md.stem + "_preprocessed.md"
        return project_dir / preprocessed_name

    def save_preprocessed_content(self, content: str, file_path: Path) -> bool:
        """保存预处理内容到文件"""
        try:
            file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logging.info(f"预处理内容已保存到: {file_path}")
            return True
        except Exception as e:
            logging.error(f"保存预处理内容失败: {e}")
            return False

    def load_preprocessed_content(self, file_path: Path) -> Optional[str]:
        """加载预处理内容"""
        try:
            if not file_path.exists(): return None
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logging.info(f"已加载预处理内容: {file_path}")
            return content
        except Exception as e:
            logging.error(f"加载预处理内容失败: {e}")
            return None

    def is_preprocessed_file_valid(self, file_path: Path, original_file_path: Path) -> bool:
        """检查预处理文件是否有效"""
        try:
            if not file_path.exists() or not original_file_path.exists(): return False
            preprocessed_mtime = file_path.stat().st_mtime
            original_mtime = original_file_path.stat().st_mtime
            return preprocessed_mtime > original_mtime
        except Exception as e:
            logging.error(f"检查预处理文件有效性失败: {e}")
            return False

def load_config(config_path: str = "config.ini") -> dict:
    """
    加载配置文件 - 使用统一的MultiChannelConfigParser

    注意：此函数保留用于向后兼容，建议直接使用MultiChannelConfigParser
    """
    from multi_channel_config import MultiChannelConfigParser

    parser = MultiChannelConfigParser(config_path)
    return parser.get_config()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 导入统一配置解析器和AI服务
    from multi_channel_config import MultiChannelConfigParser
    from ai_service import AIService

    # 使用统一配置系统
    config_parser = MultiChannelConfigParser()
    config = config_parser.get_config()

    # 创建AI服务实例
    ai_service = AIService(config)

    # 创建语义分块器
    chunker = SemanticChunker(ai_service, config=config)

    # 使用你提供的真实文本作为测试内容
    test_content = """<img alt="undefined" src="images_..._page_4_img_1.png">
四种菜品中，价格涨幅最大的是(   )。
菜品A
菜品B
菜品C
<span style="color:rgb(255, 0, 0)"><strong>莱品D</strong></span>
根据2015-2019年全国生猪出栏量情况和2015-2019年国内猪肉产量占肉类总产量的比例情况图回答下列问题。
<img alt="undefined" src="images_..._page_12_img_1.png">
由于"非洲猪痘"影响，2019年全国生猪出栏量明显下降，国家为了稳定猪肉价，大力推进生猪养殖，从2020年起全国生猪出栏量每年按照10%的速度增长，问到 ()年全国生猪出栏量超过2015年水准(   )
2025
2024
2023
<span style="color:rgb(255, 0, 0)"><strong>2022</strong></span>
3.下图反映了某种产品的成本与出厂价情况(单位:元)。根据图表回答下列问题:
<img alt="undefined" src="images_..._page_20_img_1.png">
每生产一件该产品获得的利润率最高的是第(   )年
二
三
<span style="color:rgb(255, 0, 0)"><strong>四</strong></span>
五
4.下图是过去某两年，手机用户和PC/平板用户，自主安装浏览器的情况。请根据图表信息，回答问题。
<img alt="undefined" src="images_..._page_28_img_1.png">
第一年，用户自主安装的PC/平板浏览器中，前两名的市场份额之和为(   )。
42.5%
<span style="color:rgb(255, 0, 0)"><strong>43%</strong></span>
48%
66%
5.图表是我国2月份全社会客货运输量的信息。请根据图表相关信息，回答问题。
<img alt="undefined" src="images_..._page_36_img_1.png">
如果明年2月份，货运总量的增长速度是水运货运量增长速度的2.5倍，那么明年2月份的货运总量约为(   )。
42亿吨
<span style="color:rgb(255, 0, 0)"><strong>43亿吨</strong></span>
99亿吨
98亿吨
6.10月份，几个不同移动教育应用呈现出了不同的覆盖率和活跃率，具体如下。请根据图表信息，回答问题。
<img alt="undefined" src="images_..._page_44_img_1.png">
10月份不同移动教育应用中，活跃率与覆盖率之比最大的是(   )。
作业帮
<span style="color:rgb(255, 0, 0)"><strong>阿凡题</strong></span>
纳米盒
我要当学霸
"""

    # 执行语义分块
    result = chunker.preprocess_document(test_content, "real_test_project")

    print("-" * 50)
    print(f"分块结果: 成功={result.success}")
    print(f"原始长度: {result.original_length}")
    print(f"处理后长度: {result.preprocessed_length}")
    print(f"分块数量: {result.chunk_count}")
    print(f"处理时间: {result.processing_time:.2f}s")
    print("-" * 50)

    print("\n--- 处理后内容 ---\n")
    print(result.preprocessed_content)
